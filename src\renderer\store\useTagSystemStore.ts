import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { arrayMove } from '@dnd-kit/sortable';


interface TagSystemState {
  // 状态
  activeTagIds: Set<string>;
  highlightedTagIds: Set<string>;
  isMultiSelectMode: boolean;
  lastSelectedTagId: string | null;
  caseId: number;
  matchMode: 'any' | 'all';

  // 标签看板 - 收藏的标签（将逐步被dashboardTagIds替代）
  favoriteTagIds: Set<string>;
  // 标签看板 - 显示的原子标签顺序（新）
  dashboardTagIds: string[];

  // 分栏排序 - 存储分栏的显示顺序
  sectionOrder: string[];
  // 各分栏内的原子标签顺序（除dashboard外的通用排序）
  sectionTagOrder: Record<string, string[]>;

  // 方法
  toggleTagFilter: (tagId: string, isCtrlPressed?: boolean, isShiftPressed?: boolean) => void;
  clearAllFilters: () => void;
  setMultiSelectMode: (enabled: boolean) => void;
  setHighlightedTag: (tagId: string | null) => void;
  setCaseId: (caseId: number) => void;
  setMatchMode: (mode: 'any' | 'all') => void;

  // 连选功能支持
  selectTagRange: (startTagId: string, endTagId: string, allTagIds: string[]) => void;

  // 标签看板功能（旧收藏）
  toggleFavoriteTag: (tagId: string) => void;
  isFavoriteTag: (tagId: string) => boolean;
  clearFavoriteTags: () => void;

  // 标签看板原子标签（新）
  addTagToDashboard: (tagId: string, targetIndex?: number) => void;
  removeTagFromDashboard: (tagId: string) => void;

  // 原子标签通用排序（新）
  reorderTagsInSection: (sectionId: string, oldIndex: number, newIndex: number, currentIds?: string[]) => void;

  // 分栏排序功能
  updateSectionOrder: (newOrder: string[]) => void;
  moveSectionToIndex: (sectionId: string, targetIndex: number) => void;
  resetSectionOrder: () => void;
}

import { persist } from 'zustand/middleware';

export const useTagSystemStore = create<TagSystemState>()(
  devtools(
    persist(
      (set, get) => ({
      // 初始状态
      activeTagIds: new Set<string>(),
      highlightedTagIds: new Set<string>(),
      isMultiSelectMode: false,
      lastSelectedTagId: null,
      caseId: 1, // 默认案例ID
      matchMode: 'any',
      favoriteTagIds: new Set<string>(), // 收藏的标签（旧）
      dashboardTagIds: [], // 标签看板原子标签顺序（新）
      sectionOrder: ['dashboard', 'metadata', 'cv', 'content_recognition', 'semantic_recognition', 'user'], // 默认分栏顺序
      sectionTagOrder: {}, // 各分栏内的原子标签顺序（可选覆盖后端顺序）

      // 切换标签筛选状态
      toggleTagFilter: (tagId: string, isCtrlPressed = false, isShiftPressed = false) => {
        const state = get();
        const newActiveTagIds = new Set(state.activeTagIds);

        if (isShiftPressed && state.lastSelectedTagId) {
          // Shift+点击：连选功能由外部调用selectTagRange处理
          return;
        }

        if (isCtrlPressed || state.isMultiSelectMode) {
          // Ctrl+点击或多选模式：添加/移除标签
          if (newActiveTagIds.has(tagId)) {
            newActiveTagIds.delete(tagId);
          } else {
            newActiveTagIds.add(tagId);
          }
        } else {
          // 普通点击：单选模式
          if (newActiveTagIds.has(tagId) && newActiveTagIds.size === 1) {
            // 如果只选中了这一个标签，再次点击则取消选择
            newActiveTagIds.clear();
          } else {
            // 否则只选择这个标签
            newActiveTagIds.clear();
            newActiveTagIds.add(tagId);
          }
        }

        set({
          activeTagIds: newActiveTagIds,
          lastSelectedTagId: tagId,
        });

        if (import.meta.env.VITE_DEBUG_TAG_PIPELINE === 'true') {
          console.log('[联动排查] 2. 状态Store正在更新，筛选标签为:', Array.from(newActiveTagIds));
        }
        console.log('[标签选择追踪] 1. ✅ 状态写入成功，新的 selectedTagIds 为:', Array.from(newActiveTagIds));
      },

      // 清除所有筛选
      clearAllFilters: () => {
        console.log('[标签选择追踪] 2. ⚠️ 清空操作被调用！');
        set({
          activeTagIds: new Set<string>(),
          highlightedTagIds: new Set<string>(),
          lastSelectedTagId: null,
        });

        console.log(`🏷️ [TagSystem] 清除所有标签筛选`);
      },

      // 设置多选模式
      setMultiSelectMode: (enabled: boolean) => {
        set({ isMultiSelectMode: enabled });

        if (enabled) {
          console.log(`🏷️ [TagSystem] 进入多选模式`);
        } else {
          console.log(`🏷️ [TagSystem] 退出多选模式`);
        }
      },

      // 设置高亮标签
      setHighlightedTag: (tagId: string | null) => {
        const newHighlightedTagIds = new Set<string>();
        if (tagId) {
          newHighlightedTagIds.add(tagId);
        }

        set({ highlightedTagIds: newHighlightedTagIds });
      },

      // 设置案例ID
      setCaseId: (caseId: number) => {
        // 切换案例时清除所有筛选
        set({
          caseId,
          activeTagIds: new Set<string>(),
          highlightedTagIds: new Set<string>(),
          lastSelectedTagId: null,
        });

        console.log(`🏷️ [TagSystem] 切换到案例: ${caseId}`);
      },

      // 切换匹配模式
      setMatchMode: (mode: 'any' | 'all') => {
        set({ matchMode: mode });
        console.log(`🏷️ [TagSystem] 切换匹配模式: ${mode}`);
      },

      // 连选功能
      selectTagRange: (startTagId: string, endTagId: string, allTagIds: string[]) => {
        const state = get();
        const newActiveTagIds = new Set(state.activeTagIds);

        const startIndex = allTagIds.indexOf(startTagId);
        const endIndex = allTagIds.indexOf(endTagId);

        if (startIndex === -1 || endIndex === -1) {
          console.warn(`🏷️ [TagSystem] 连选失败: 找不到标签索引`);
          return;
        }

        const minIndex = Math.min(startIndex, endIndex);
        const maxIndex = Math.max(startIndex, endIndex);

        // 选择范围内的所有标签
        for (let i = minIndex; i <= maxIndex; i++) {
          newActiveTagIds.add(allTagIds[i]);
        }

        set({
          activeTagIds: newActiveTagIds,
          lastSelectedTagId: endTagId,
        });

        console.log(`🏷️ [TagSystem] 连选范围: ${minIndex}-${maxIndex}, 共${maxIndex - minIndex + 1}个标签`);
      },

      // 标签看板功能（旧收藏）
      toggleFavoriteTag: (tagId: string) => {
        const state = get();
        const newFavoriteTagIds = new Set(state.favoriteTagIds);

        if (newFavoriteTagIds.has(tagId)) {
          newFavoriteTagIds.delete(tagId);
          console.log(`⭐ [TagSystem] 取消收藏标签: ${tagId}`);
        } else {
          newFavoriteTagIds.add(tagId);
          console.log(`⭐ [TagSystem] 收藏标签: ${tagId}`);
        }

        set({ favoriteTagIds: newFavoriteTagIds });
      },

      isFavoriteTag: (tagId: string) => {
        return get().favoriteTagIds.has(tagId);
      },

      clearFavoriteTags: () => {
        set({ favoriteTagIds: new Set<string>() });
        console.log(`⭐ [TagSystem] 清空所有收藏标签`);
      },

      // 标签看板原子标签（新）
      addTagToDashboard: (tagId: string, targetIndex?: number) => {
        const { dashboardTagIds } = get();
        if (dashboardTagIds.includes(tagId)) {
          console.log(`ℹ️ [TagSystem] 看板已包含: ${tagId}`);
          return;
        }
        const updated = [...dashboardTagIds];
        if (typeof targetIndex === 'number' && targetIndex >= 0 && targetIndex <= updated.length) {
          updated.splice(targetIndex, 0, tagId);
        } else {
          updated.push(tagId);
        }
        set({ dashboardTagIds: updated });
        console.log(`📌 [TagSystem] 添加到看板: ${tagId} @ ${typeof targetIndex === 'number' ? targetIndex : 'end'}`);
      },

      removeTagFromDashboard: (tagId: string) => {
        const { dashboardTagIds } = get();
        set({ dashboardTagIds: dashboardTagIds.filter((id) => id !== tagId) });
        console.log(`📌 [TagSystem] 从看板移除: ${tagId}`);
      },

      reorderTagsInSection: (sectionId: string, oldIndex: number, newIndex: number, currentIds?: string[]) => {
        // 通用化排序：支持任意允许排序的分栏
        // 1) dashboard：使用 dashboardTagIds
        if (sectionId === 'dashboard') {
          const { dashboardTagIds } = get();
          set({ dashboardTagIds: arrayMove(dashboardTagIds, oldIndex, newIndex) });
          console.log(`📌 [TagSystem] 看板内部排序: ${oldIndex} -> ${newIndex}`);
          return;
        }
        // 2) 其他分栏：使用 sectionTagOrder 的覆盖顺序；首次使用时初始化为当前显示顺序
        const { sectionTagOrder } = get();
        const base = sectionTagOrder[sectionId] && sectionTagOrder[sectionId]!.length > 0
          ? sectionTagOrder[sectionId]!
          : (currentIds && currentIds.length > 0 ? [...currentIds] : []);
        if (base.length === 0) {
          console.warn(`⚠️ [TagSystem] ${sectionId} 缺少本地排序且无当前顺序，放弃排序`);
          return;
        }
        const updated = arrayMove(base, oldIndex, newIndex);
        set({ sectionTagOrder: { ...sectionTagOrder, [sectionId]: updated } });
        console.log(`📌 [TagSystem] 分栏(${sectionId})内部排序: ${oldIndex} -> ${newIndex}`);
      },

      // 分栏排序功能
      updateSectionOrder: (newOrder: string[]) => {
        set({ sectionOrder: newOrder });
        console.log(`🔄 [TagSystem] 更新分栏顺序:`, newOrder);
      },

      moveSectionToIndex: (sectionId: string, targetIndex: number) => {
        const state = get();
        const currentOrder = [...state.sectionOrder];
        const currentIndex = currentOrder.indexOf(sectionId);

        if (currentIndex === -1) {
          console.warn(`⚠️ [TagSystem] 未找到分栏: ${sectionId}`);
          return;
        }

        // 移除当前位置的元素
        currentOrder.splice(currentIndex, 1);
        // 插入到目标位置
        currentOrder.splice(targetIndex, 0, sectionId);

        set({ sectionOrder: currentOrder });
        console.log(`🔄 [TagSystem] 移动分栏 ${sectionId} 到位置 ${targetIndex}:`, currentOrder);
      },

      resetSectionOrder: () => {
        const defaultOrder = ['dashboard', 'metadata', 'cv', 'content_recognition', 'semantic_recognition', 'user'];
        set({ sectionOrder: defaultOrder });
        console.log(`🔄 [TagSystem] 重置分栏顺序为默认:`, defaultOrder);
      },
    }),
    {
      name: 'tag-system-storage',
      partialize: (state) => ({
        dashboardTagIds: state.dashboardTagIds,
        sectionTagOrder: state.sectionTagOrder,
        sectionOrder: state.sectionOrder,
      }),
      // 关键：自定义 merge，深度合并并优先保留 currentState（内存中的最新状态）
      merge: (persistedState: any, currentState: any) => {
        try {
          const merged = { ...currentState };
          const p = persistedState || {};

          // 仅合并我们关心的三个字段，且仅在 currentState 为空或未设置时才采用持久化的值
          // 否则保留 currentState（避免覆盖内存中刚刚完成的更新）
          if (Array.isArray(p.dashboardTagIds) && (!Array.isArray(currentState.dashboardTagIds) || currentState.dashboardTagIds.length === 0)) {
            merged.dashboardTagIds = [...p.dashboardTagIds];
          }

          if (p.sectionTagOrder && typeof p.sectionTagOrder === 'object') {
            // 基于 currentState.sectionTagOrder 做浅合并，currentState 优先
            const curMap = currentState.sectionTagOrder || {};
            const persistedMap = p.sectionTagOrder || {};
            merged.sectionTagOrder = { ...persistedMap, ...curMap };
          }

          if (Array.isArray(p.sectionOrder) && (!Array.isArray(currentState.sectionOrder) || currentState.sectionOrder.length === 0)) {
            merged.sectionOrder = [...p.sectionOrder];
          }

          return { ...currentState, ...merged };
        } catch (e) {
          console.warn('[useTagSystemStore] merge failed, fallback to currentState', e);
          return currentState;
        }
      },
    }
  )
  )
);

// 导出类型
export type { TagSystemState };

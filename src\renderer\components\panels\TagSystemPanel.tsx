// 标签系统面板 - 完全恢复历史版本样式和功能
import React, { useState, useEffect, useRef, useMemo } from 'react';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { reorderTagsInSectionSafe } from './utils/sortTags';
import { useTagSystemStore } from '../../store/useTagSystemStore';
import { getTags, queryKeys, handleApiError } from '../../services/api';
import { useTagDataStore } from '@/store/useTagDataStore';
import { useActiveCaseId } from '@/hooks/useActiveCase';
// import { isElectronEnvironment } from '../../utils/electronDragDebug';
import { DndContext, closestCenter, DragEndEvent, useDroppable, PointerSensor, useSensor, useSensors } from '@dnd-kit/core';
import { SortableContext, verticalListSortingStrategy } from '@dnd-kit/sortable';
import SortableTagSection from './SortableTagSection';
import SortableTag from './SortableTag';
import { apiClient } from '@/lib/apiClient';
import { useSelectionStore } from '@/store/useSelectionStore';
import { useFileIdMapping } from '@/store/useFileSyncStore';

// 🎯 真实的标签数据类型定义
export interface Tag {
  id: string;
  name: string;
  type?: string;
  category?: string;
  count?: number;
  file_ids?: number[];
  color?: string;
}

export interface TagSection {
  id: string;
  title: string;
  tags: Tag[];
  count: number;
  icon?: string;
}

export interface TagsResponse {
  sections: TagSection[];
  total_tags: number;
  case_id: number;
}

// 🎯 默认标签分类结构 - 根据《终极需求.md》定义，拆分为6个分栏（新增标签看板）
const DEFAULT_TAG_SECTIONS: TagSection[] = [
  {
    id: 'dashboard',
    title: '标签看板',
    tags: [],
    count: 0,
    icon: '⭐'
  },
  {
    id: 'metadata',
    title: '元数据',
    tags: [],
    count: 0,
    icon: '📊'
  },
  {
    id: 'cv',
    title: '计算机视觉',
    tags: [],
    count: 0,
    icon: '👁️'
  },
  {
    id: 'content_recognition',
    title: '内容识别',
    tags: [],
    count: 0,
    icon: '🔍'
  },
  {
    id: 'semantic_recognition',
    title: '语义识别',
    tags: [],
    count: 0,
    icon: '🧠'
  },
  {
    id: 'user',
    title: '用户标签',
    tags: [],
    count: 0,
    icon: '🏷️'
  }
];

// 🎨 完整的样式系统 - 恢复历史版本
const styles = {
  colors: {
    galleryWorkbenchBg: '#18191C',
    sidePanelBg: '#1F2023',
    resizeHandle: '#353639',
    resizeHandleHover: '#9D362F',
    btnSpecialBg: '#2A2B2E',
    btnHoverBg: '#353639',
    btnClickBorder: '#7F2C25',
    baseBorder: '#353639',
    inputBg: '#131416',
    icon: '#F7F8F8',
    iconHover: '#9D362F',
    titleText: '#F7F8F8',
    interactiveTextHover: '#9D362F',
    contentText: '#9F9FA2',
  },
  font: {
    family: '"Microsoft YaHei Light", sans-serif',
    size: {
      xs: '10px',
      sm: '12px',
      base: '14px',
    },
  },
  spacing: '4px',
  panelPadding: '12px 4px',
};

// 🏷️ TagPill组件 - 单个标签按钮
interface TagPillProps {
  tag: Tag;
  isActive: boolean;
  isHighlighted: boolean;
  isFavorite?: boolean;
  onClick: (tagId: string, event: React.MouseEvent) => void;
  onMouseEnter: () => void;
  onMouseLeave: () => void;
}

interface AddUserTagCardProps {}

const AddUserTagCard: React.FC<AddUserTagCardProps> = () => {
  const [isEditing, setIsEditing] = React.useState(false);
  const [value, setValue] = React.useState('');
  const inputRef = React.useRef<HTMLInputElement | null>(null);
  const { selectedFiles } = useSelectionStore.getState();
  const { caseId } = useTagSystemStore.getState();
  const queryClient = useQueryClient();
  // 将 Hook 提升到组件顶层，遵守 Rules of Hooks
  const fileIdMapper = useFileIdMapping();

  React.useEffect(() => {
    if (isEditing && inputRef.current) inputRef.current.focus();
  }, [isEditing]);

  const cancel = () => {
    setValue('');
    setIsEditing(false);
  };

  const submit = async () => {
    const name = value.trim();
    if (!name) {
      cancel();
      return;
    }
    try {
      // 1) 先创建自定义标签（0聚合允许）
      const created = await apiClient.createCustomTag(caseId, name);
      // 2) 若存在选中图片，批量应用该标签到选中图片（通过 batch-actions add_tags，写入 tags.user）
      const fileIds: number[] = [];
      for (const f of selectedFiles || []) {
        const id = fileIdMapper.getFileId(f.path);
        if (id) fileIds.push(id);
      }
      if (fileIds.length > 0) {
        await apiClient.batchAddTags(fileIds, name);
      }
    } catch (e) {
      console.error('创建自定义标签失败', e);
    } finally {
      cancel();
      // 刷新聚合标签树
      queryClient.invalidateQueries({ queryKey: queryKeys.tags(caseId) });
    }
  };

  // 全局点击外部取消
  React.useEffect(() => {
    if (!isEditing) return;
    const onDocClick = (e: MouseEvent) => {
      if (!inputRef.current) return;
      if (!inputRef.current.contains(e.target as Node)) {
        cancel();
      }
    };
    document.addEventListener('mousedown', onDocClick);
    return () => document.removeEventListener('mousedown', onDocClick);
  }, [isEditing]);

  if (!isEditing) {
    return (
      <button
        className="inline-flex items-center gap-1.5 px-2.5 py-1 rounded-md border text-xs font-medium border-dashed border-gray-500/50 text-gray-400 hover:text-gray-300 hover:border-gray-400/70 hover:bg-gray-500/10 transition-colors"
        onClick={() => setIsEditing(true)}
        title="新增标签"
      >
        <span>新增标签</span>
      </button>
    );
  }

  return (
    <input
      ref={inputRef}
      value={value}
      onChange={(e) => setValue(e.target.value)}
      onKeyDown={(e) => {
        if (e.key === 'Enter') submit();
        if (e.key === 'Escape') cancel();
      }}
      onBlur={cancel}
      placeholder="输入标签名并回车"
      className="px-2.5 py-1 text-xs bg-[#131416] border border-[#353639] rounded-md text-[#F7F8F8] placeholder-[#9F9FA2] focus:outline-none focus:border-[#9D362F] min-w-[140px]"
    />
    );

}

const TagPill: React.FC<TagPillProps> = ({
  tag,
  isActive,
  isHighlighted,
  onClick,
  onMouseEnter,
  onMouseLeave,
}) => {
  const [isHovered, setIsHovered] = useState(false);

  return (
    <div
      className="inline-flex items-center group"
      onMouseEnter={() => {
        setIsHovered(true);
        onMouseEnter();
      }}
      onMouseLeave={() => {
        setIsHovered(false);
        onMouseLeave();
      }}
    >
      <button
        className="inline-flex items-center px-2 py-1 transition-all duration-200"
        style={{
          backgroundColor: 'transparent',
          color: styles.colors.titleText,
          fontFamily: styles.font.family,
          fontSize: styles.font.size.sm,
          borderRadius: '2px',
          border: isActive
            ? `1px solid ${styles.colors.btnClickBorder}`
            : isHighlighted
              ? `1px solid ${styles.colors.resizeHandleHover}`
              : '1px solid transparent',
          outline: 'none',
          boxShadow: 'none',
        }}
        onClick={(event) => {
          if (import.meta.env.VITE_DEBUG_TAG_PIPELINE === 'true') {
            console.log('[联动排查] 1. 标签被点击:', tag);
          }
          console.log('[线路检查] TagPill收到的onClick prop:', typeof onClick);
          onClick(tag.id, event);
        }}
        title={`${tag.name} (${tag.count || 0})${tag.category ? ` - 来自${tag.category}` : ''}`}
      >
        <span>{tag.name}</span>
        {tag.count && (
          <span
            className="ml-1"
            style={{
              color: styles.colors.contentText,
              fontSize: styles.font.size.xs
            }}
          >
            {tag.count}
          </span>
        )}
      </button>


    </div>
  );
};

// 🏷️ TagSectionComponent - 标签分组组件
interface TagSectionProps {
  section: TagSection;
  activeTagIds: Set<string>;
  highlightedTagIds: Set<string>;
  favoriteTagIds?: Set<string>;
  onTagClick: (tagId: string, event: React.MouseEvent) => void;
  onTagHover: (tagId: string | null) => void;
  onToggleFavorite?: (tagId: string, event: React.MouseEvent) => void;
  onClearFavorites?: () => void;

  // 拖拽相关（简化）：仅负责被拖拽，不负责放置
  isDragging?: boolean;
  isDragOver?: boolean;
  onDragStart?: (sectionId: string, e?: React.DragEvent) => void;
  onDragEnd?: (e?: React.DragEvent) => void;
  // 来自 SortableTagSection 的拖拽手柄监听
  dragListeners?: React.HTMLAttributes<HTMLElement>;
}

const TagSectionComponent: React.FC<TagSectionProps> = ({
  section,
  activeTagIds,
  highlightedTagIds,
  favoriteTagIds = new Set(),
  onTagClick,
  onTagHover,
  onToggleFavorite,
  onClearFavorites,
  isDragging = false,
  isDragOver = false,
  onDragStart,
  onDragEnd,
  onDragOver,
  onDrop,
  dragListeners,
}) => {
  // 🎯 空分类默认折叠，有内容的分类默认展开
  const [isExpanded, setIsExpanded] = useState(section.tags.length > 0);

  // 将该分栏的标签列表区域声明为 Droppable 容器
  const { setNodeRef: setDroppableRef } = useDroppable({ id: section.id, data: { type: 'Section' } });

  return (
    <div
      className="mb-3"
      style={{
        lineHeight: '1.0',
        marginBottom: '20px',
      }}
      draggable
      onDragStart={(e) => onDragStart?.(section.id, e)}
      onDragEnd={(e) => onDragEnd?.(e)}
    >
      {/* 分类标题 */}
      <div
        className="w-full flex items-center justify-between p-1"
        style={{
          backgroundColor: styles.colors.galleryWorkbenchBg,
          borderTop: `1px solid ${styles.colors.btnSpecialBg}`,
          margin: '0 -8px',
          padding: '8px',
        }}
      >
        {/* 左侧：分类名称按钮 */}
        <button
          className="text-left transition-colors hover:bg-opacity-20"
          style={{
            backgroundColor: 'transparent',
            color: styles.colors.titleText,
            fontFamily: styles.font.family,
            fontSize: styles.font.size.sm,
            border: 'none',
            padding: '4px 8px',
            borderRadius: '2px',
          }}
          onMouseEnter={(e) => {
            (e.target as HTMLElement).style.backgroundColor = styles.colors.btnHoverBg;
          }}
          onMouseLeave={(e) => {
            (e.target as HTMLElement).style.backgroundColor = 'transparent';
          }}
          onClick={() => setIsExpanded(!isExpanded)}
        >
          <span className="flex items-center gap-2">
            {/* 拖拽手柄 */}
            <span
              className="cursor-move opacity-50 hover:opacity-100 transition-opacity"
              style={{ fontSize: '10px' }}
              title="拖拽排序"
              {...(dragListeners || {})}
              onClick={(e) => e.stopPropagation()}
            >
              ⋮⋮
            </span>
            {section.icon && <span>{section.icon}</span>}
            <span>{section.title}</span>
            <span style={{ color: styles.colors.contentText }}>
              ({section.count})
            </span>
          </span>
        </button>

        {/* 右侧：标签看板的清空按钮 + 展开/折叠按钮 */}
        <div className="flex items-center gap-1">
          {section.id === 'dashboard' && section.count > 0 && onClearFavorites && (
            <button
              className="transition-colors hover:bg-opacity-20"
              style={{
                backgroundColor: 'transparent',
                color: styles.colors.contentText,
                fontFamily: styles.font.family,
                fontSize: '10px',
                border: 'none',
                padding: '2px 4px',
                borderRadius: '2px',
              }}
              onClick={(e) => {
                e.stopPropagation();
                onClearFavorites();
              }}
              title="清空收藏"
              onMouseEnter={(e) => {
                (e.target as HTMLElement).style.backgroundColor = styles.colors.btnHoverBg;
              }}
              onMouseLeave={(e) => {
                (e.target as HTMLElement).style.backgroundColor = 'transparent';
              }}
            >
              🗑️
            </button>
          )}

          <button
            className="transition-all"
            onClick={() => setIsExpanded(!isExpanded)}
            style={{
              backgroundColor: 'transparent',
              color: styles.colors.titleText,
              fontFamily: styles.font.family,
              fontSize: styles.font.size.sm,
              border: 'none',
              padding: '2px 4px',
              transform: isExpanded ? 'rotate(90deg)' : 'rotate(0deg)',
            }}
          >
            ▶
          </button>
        </div>
      </div>

      {/* 标签列表 */}
      {isExpanded && (
        <div
          className="pt-1"
          style={{
            paddingLeft: styles.spacing,
            paddingRight: styles.spacing,
            marginLeft: styles.spacing,
            marginRight: styles.spacing,
          }}
        >
          <div
            ref={setDroppableRef}
            className="flex flex-wrap gap-1"
            style={{
              maxWidth: '100%',
              overflow: 'hidden',
            }}
          >
            {section.tags.length > 0 ? (
              <SortableContext items={section.tags.map(t => String(t.id))} strategy={verticalListSortingStrategy}>
                {section.tags.map((tag) => (
                  <SortableTag key={tag.id} id={String(tag.id)} containerId={section.id}>
                    <TagPill
                      tag={tag}
                      isActive={activeTagIds.has(tag.id)}
                      isHighlighted={highlightedTagIds.has(tag.id)}
                      onClick={onTagClick}
                      onMouseEnter={() => onTagHover(tag.id)}
                      onMouseLeave={() => onTagHover(null)}
                    />
                  </SortableTag>
                ))}

                {/* 【新增标签】卡片，仅在用户标签分栏显示 */}
                {section.id === 'user' && (
                  <AddUserTagCard />
                )}

              </SortableContext>
            ) : (
              <div className="flex items-center gap-2 py-2">
                <div
                  className="text-left"
                  style={{
                    color: styles.colors.contentText,
                    fontSize: styles.font.size.xs,
                    fontFamily: styles.font.family,
                    fontStyle: 'italic'
                  }}
                >
                  {section.id === 'dashboard'
                    ? '暂无收藏标签，点击其他分类中标签旁的⭐来收藏'
                    : `暂无${section.title}标签`
                  }
                </div>
                {section.id === 'user' && (
                  <AddUserTagCard />
                )}
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

// 🏷️ 主组件接口
interface TagSystemPanelProps {
  className?: string;
}

// 🔧 合并实际数据和默认结构的工具函数 - 已抽取到 utils/mergeSections
import { mergeWithDefaultSections as mergeSectionsUtil } from './utils/mergeSections';


// 🏷️ TagSystemPanel主组件
const TagSystemPanel: React.FC<TagSystemPanelProps> = ({ className }) => {
  const {
    activeTagIds,
    highlightedTagIds,
    isMultiSelectMode,
    caseId,
    toggleTagFilter,
    clearAllFilters,
    setMultiSelectMode,
    setHighlightedTag,
    setCaseId,
    selectTagRange,
    favoriteTagIds,
    toggleFavoriteTag,
    isFavoriteTag,
    clearFavoriteTags,
    // 订阅式读取，确保排序更新后触发重渲染
    dashboardTagIds,
    sectionTagOrder,
    sectionOrder,
    updateSectionOrder,
    moveSectionToIndex,
    resetSectionOrder,
  } = useTagSystemStore();

  // 基于 dnd-kit 的拖拽结束回调
  const handleDndDragEnd = (event: DragEndEvent) => {
    console.log('--- 🎯 DND Drag End Triggered ---', event);
    const type = (event.active.data.current as any)?.type;
    if (type === 'Tag') return handleTagDragEnd(event);

    // 默认按分栏处理
    const { active, over } = event;
    console.log(`➡️ Active (Dragged) Item ID: ${active.id}`);
    console.log(`⬅️ Over (Drop Target) Item ID: ${over?.id}`);
    if (!over) {
      console.log('❌ Drop failed: No valid drop target (over is null).');
      return;
    }
    if (active.id === over.id) {
      console.log('↔️ Drop ignored: Item dropped on itself.');
      return;
    }

    const oldIndex = displaySections.findIndex(s => s.id === String(active.id));
    const newIndex = displaySections.findIndex(s => s.id === String(over.id));
    console.log(`Section reorder: oldIndex=${oldIndex}, newIndex=${newIndex}`);
    if (oldIndex === -1 || newIndex === -1 || oldIndex === newIndex) return;

    moveSectionToIndex(String(active.id), newIndex);
  };

  // 原子标签拖拽处理
  const handleTagDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;
    console.log('[TagDragEnd] active:', active?.id, 'over:', over?.id);
    if (!over) {
      console.log('❌ [TagDragEnd] over is null');
      return;
    }

    const activeType = (active.data.current as any)?.type;
    const srcContainer = (active.data.current as any)?.containerId as string | undefined;

    // 修复：优先识别 over 为标签项时的 containerId；否则将 over.id 视为容器ID
    const overIsTag = (over.data.current as any)?.type === 'Tag';
    const dstContainer = overIsTag
      ? ((over.data.current as any)?.containerId as string | undefined)
      : (String(over.id));

    console.log(`[TagDragEnd] type=${activeType}, src=${srcContainer}, dst=${dstContainer}`);

    if (activeType !== 'Tag') return; // 只处理标签

    // 允许规则：
    // 1) 在同一容器内部排序（受限于白名单）
    // 2) 跨容器时仅允许拖入 dashboard

    const sortableSections = ['dashboard', 'metadata', 'user']; // 可根据业务扩展

    if (srcContainer && srcContainer === dstContainer) {
      if (!sortableSections.includes(srcContainer)) {
        console.log(`🚫 [TagDragEnd] Section ${srcContainer} not sortable, ignore`);
        return;
      }
      // 从 displaySections 获取当前容器的实际标签顺序
      const srcSection = displaySections.find(s => s.id === srcContainer);
      if (!srcSection) {
        console.log(`❌ [TagDragEnd] Source section ${srcContainer} not found`);
        return;
      }
      const ids = srcSection.tags.map(t => String(t.id));
      const oldIndex = ids.indexOf(String(active.id));
      const newIndex = ids.indexOf(String(over.id));
      console.log(`[TagDragEnd] reorder in ${srcContainer}: old=${oldIndex}, new=${newIndex}`);
      if (oldIndex !== -1 && newIndex !== -1 && oldIndex !== newIndex) {
        useTagSystemStore.getState().reorderTagsInSection(srcContainer, oldIndex, newIndex, ids);
        console.log('✅ [TagDragEnd] reorderTagsInSection dispatched');
      } else {
        console.log('❌ [TagDragEnd] index calc failed (same item or not found)');
      }
      return;
    }

    // 跨容器添加：仅允许拖入 dashboard
    if (dstContainer === 'dashboard') {
      useTagSystemStore.getState().addTagToDashboard(String(active.id));
      console.log(`✅ [TagDragEnd] addTagToDashboard(${String(active.id)}) dispatched`);
    } else {
      console.log(`🚫 [TagDragEnd] Cross-container drop to ${dstContainer} not allowed`);
    }
  };

  const panelRef = useRef<HTMLDivElement>(null);

  // Electron 诊断逻辑已移除（迁移至 dnd-kit，不再需要）

  // 初始化与同步案例ID：以 useUIStore 的 activeCaseId 为唯一来源
  const { activeCaseId, isValidCaseId } = useActiveCaseId();
  useEffect(() => {
    if (isValidCaseId && typeof activeCaseId === 'number' && activeCaseId !== caseId) {
      setCaseId(activeCaseId);
    }
  }, [isValidCaseId, activeCaseId, caseId, setCaseId]);

  // 获取标签数据
  const {
    data: tagsData,
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: queryKeys.tags(caseId),
    queryFn: () => getTags(caseId),
    // 高嫌疑点：在 selector/选择器阶段丢数据？这里记录 selector 输入
    select: (data) => {
      if (import.meta.env.VITE_DEBUG_TAG_PIPELINE === 'true') {
        console.log('[荧光染色] 1.5 Selector输入(紧接API):', data);
      }
      return data;
    },
    staleTime: 5 * 60 * 1000,
    gcTime: 10 * 60 * 1000,
    enabled: isValidCaseId && typeof activeCaseId === 'number' && caseId === activeCaseId,
  });

  // 监听键盘事件
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.ctrlKey || event.shiftKey) {
        setMultiSelectMode(true);
      }
    };

    const handleKeyUp = (event: KeyboardEvent) => {
      if (!event.ctrlKey && !event.shiftKey) {
        setMultiSelectMode(false);
      }
    };

    const handleClickOutside = (event: MouseEvent) => {
      if (panelRef.current && !panelRef.current.contains(event.target as Node)) {
        const targetEl = event.target as Element | null;
        const inGallery = !!targetEl?.closest('.gallery-container');
        const inPreview = !!targetEl?.closest('[data-preview-window]');
        const inDetailsPanel = !!targetEl?.closest('[data-details-panel]');
        if (inGallery || inPreview || inDetailsPanel) return; // 不要在画廊、预览窗口、右侧信息栏中点击时清除筛选
        clearAllFilters();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    document.addEventListener('keyup', handleKeyUp);
    document.addEventListener('mousedown', handleClickOutside);

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      document.removeEventListener('keyup', handleKeyUp);
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [setMultiSelectMode, clearAllFilters]);

  // 处理标签点击：直接使用全局筛选引擎（点击即筛选，支持Ctrl/Shift由store处理）
  const handleTagClick = (tagId: string, event: React.MouseEvent) => {
    event.stopPropagation();
    toggleTagFilter(tagId, event.ctrlKey || event.metaKey, event.shiftKey);
  };

  // 保留旧的连选逻辑入口（已由 useSelectionLogic 接管，无需手动处理）

  // 处理标签悬停
  const handleTagHover = (tagId: string | null) => {
    setHighlightedTag(tagId);
  };

  // 处理清除所有筛选
  const handleClearAll = () => {
    clearAllFilters();
  };

  // 处理刷新
  const handleRefresh = () => {
    refetch();
  };

  const activeTagCount = activeTagIds.size;

  // 🎯 合并实际数据和默认分类结构
  if (import.meta.env.VITE_DEBUG_TAG_PIPELINE === 'true') {
    console.log('[荧光染色] 4. 最终用于渲染前的API数据:', tagsData);
  }
  const displaySections = React.useMemo(() => {
    return mergeSectionsUtil(tagsData?.sections as any, DEFAULT_TAG_SECTIONS as any, {
      favoriteTagIds,
      dashboardTagIds,
      sectionTagOrder: sectionTagOrder as any,
    }) as any;
  }, [tagsData?.sections, favoriteTagIds, dashboardTagIds, sectionTagOrder]);
  // 将最终分栏数据存入全局共享 store，供右侧信息栏消费
  useEffect(() => {
    try {
      useTagDataStore.getState().setSections(displaySections as any);
    } catch (e) {
      console.warn('[TagSystemPanel] setSections failed:', e);
    }
  }, [JSON.stringify(displaySections)]);
  if (import.meta.env.VITE_DEBUG_TAG_PIPELINE === 'true') {
    console.log('[荧光染色] 5. 渲染前的最终分栏数据:', displaySections);
  }
  const totalTags = tagsData?.total_tags || 0;

  // 配置 DnD 传感器：拖动超过 5px 才激活拖拽，避免误吸收点击
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: { distance: 5 },
    })
  );

  return (
    <div
      ref={panelRef}
      className={`w-full h-full flex flex-col ${className || ''}`}
      style={{
        backgroundColor: styles.colors.sidePanelBg,
        padding: styles.panelPadding,
        fontFamily: styles.font.family,
        fontSize: styles.font.size.sm,
      }}
    >
      {/* 标题栏 */}
      <div
        className="flex items-center justify-between mb-3"
        style={{
          color: styles.colors.titleText,
          fontSize: styles.font.size.sm,
          fontFamily: styles.font.family,
        }}
      >
        <div className="flex items-center gap-2">
          <span>标签筛选</span>
          {isMultiSelectMode && (
            <span
              style={{
                backgroundColor: 'rgba(157, 54, 47, 0.1)',
                padding: '2px 6px',
                borderRadius: '2px',
                border: `1px solid ${styles.colors.interactiveTextHover}`
              }}
            >
              多选
            </span>
          )}
        </div>
        <div className="flex items-center gap-2">
          {activeTagCount > 0 && (
            <button
              onClick={handleClearAll}
              className="transition-colors"
              style={{
                color: styles.colors.titleText,
                fontSize: styles.font.size.sm,
                fontFamily: styles.font.family,
                backgroundColor: 'transparent',
                border: 'none',
              }}
              onMouseEnter={(e) => (e.target as HTMLElement).style.color = styles.colors.interactiveTextHover}
              onMouseLeave={(e) => (e.target as HTMLElement).style.color = styles.colors.titleText}
            >
              清除 ({activeTagCount})
            </button>
          )}
          <button
            onClick={resetSectionOrder}
            className="transition-colors"
            style={{
              color: styles.colors.titleText,
              fontSize: styles.font.size.sm,
              fontFamily: styles.font.family,
              backgroundColor: 'transparent',
              border: 'none',
            }}
            onMouseEnter={(e) => (e.target as HTMLElement).style.color = styles.colors.interactiveTextHover}
            onMouseLeave={(e) => (e.target as HTMLElement).style.color = styles.colors.titleText}
            title="重置分栏排序"
          >
            🔄
          </button>

          <button
            onClick={handleRefresh}
            className="transition-colors"
            style={{
              color: styles.colors.titleText,
              fontSize: styles.font.size.sm,
              fontFamily: styles.font.family,
              backgroundColor: 'transparent',
              border: 'none',
            }}
            onMouseEnter={(e) => (e.target as HTMLElement).style.color = styles.colors.interactiveTextHover}
            onMouseLeave={(e) => (e.target as HTMLElement).style.color = styles.colors.titleText}
          >
            刷新
          </button>
        </div>
      </div>

      {/* 标签内容 */}
      <div className="flex-1 custom-scrollbar">
        {isLoading ? (
          <div className="text-center mt-8">
            <div className="text-2xl mb-2">⏳</div>
            <p style={{
              color: styles.colors.titleText,
              fontSize: styles.font.size.sm,
              fontFamily: styles.font.family
            }}>
              加载标签中...
            </p>
          </div>
        ) : error ? (
          <div className="text-center mt-8">
            <div className="text-2xl mb-2">❌</div>
            <p style={{
              color: styles.colors.interactiveTextHover,
              fontSize: styles.font.size.sm,
              fontFamily: styles.font.family
            }}>
              加载标签失败
            </p>
            <p style={{
              color: styles.colors.contentText,
              fontSize: styles.font.size.xs,
              fontFamily: styles.font.family,
              marginTop: '4px'
            }}>
              {handleApiError(error)}
            </p>
            <button
              onClick={handleRefresh}
              style={{
                marginTop: '8px',
                padding: '4px 8px',
                backgroundColor: styles.colors.interactiveTextHover,
                color: styles.colors.titleText,
                border: 'none',
                borderRadius: '2px',
                fontSize: styles.font.size.sm,
                fontFamily: styles.font.family,
              }}
            >
              重试
            </button>
          </div>
        ) : (
          // 🎯 使用 dnd-kit 的可排序上下文
          <DndContext sensors={sensors} collisionDetection={closestCenter} onDragEnd={handleDndDragEnd}>
            <SortableContext items={displaySections.map(s => s.id)} strategy={verticalListSortingStrategy}>
              {displaySections.map((section) => {
                console.log('🧪 [TagSystem] Rendering section:', section.id, 'with tags:', section.tags?.length ?? 0, section);
                return (
                  <SortableTagSection key={section.id} id={section.id}>
                    <TagSectionComponent
                      section={section}
                      activeTagIds={activeTagIds}
                      highlightedTagIds={highlightedTagIds}
                      onTagClick={handleTagClick}
                      onTagHover={handleTagHover}
                      onClearFavorites={clearFavoriteTags}
                    />
                  </SortableTagSection>
                );
              })}
            </SortableContext>
          </DndContext>
        )}
      </div>

      {/* 底部状态 - 始终显示 */}
      <div
        className="mt-2 pt-2 border-t flex justify-between items-center"
        style={{
          borderColor: styles.colors.baseBorder,
          color: styles.colors.titleText,
          fontSize: styles.font.size.sm,
          fontFamily: styles.font.family
        }}
      >
        <span>总计: {totalTags}</span>
        <span>已选: {activeTagCount}</span>
      </div>
    </div>
  );
};

export default TagSystemPanel;
